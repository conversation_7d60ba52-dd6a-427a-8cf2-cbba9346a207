import React, { useState } from 'react';
import { Helmet } from 'react-helmet';
import { motion } from 'framer-motion';
import { ArrowRight, Code2, Globe2, Smartphone, Database, Shield, Sparkles, ExternalLink } from 'lucide-react';

const Portfolio = () => {
      

  const [activeFilter, setActiveFilter] = useState('all');

  const categories = [
    { id: 'all', label: 'All Projects' },
    { id: 'web', label: 'Web Development', icon: Globe2 },
    { id: 'mobile', label: 'Mobile Apps', icon: Smartphone },
    { id: 'enterprise', label: 'Enterprise Solutions', icon: Database },
    { id: 'security', label: 'Security Systems', icon: Shield }
  ];

  const projects = [
    {
      id: 1,
      title: "E-Commerce Platform",
      category: "web",
      image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=500&fit=crop&q=80",
      description: "A full-featured e-commerce solution with advanced inventory management.",
      technologies: ["React", "Node.js", "MongoDB", "AWS"],
      client: "RetailTech Solutions",
      duration: "4 months",
      link: "#"
    },
    {
      id: 2,
      title: "Healthcare Management System",
      category: "enterprise",
      image: "https://images.unsplash.com/photo-1576091160399-112ba8d25d1d?w=800&h=500&fit=crop&q=80",
      description: "Comprehensive healthcare management platform for hospitals.",
      technologies: ["Angular", "Java", "PostgreSQL", "Docker"],
      client: "MedCare Hospitals",
      duration: "6 months",
      link: "#"
    },
    {
      id: 3,
      title: "Fitness Tracking App",
      category: "mobile",
      image: "https://images.unsplash.com/photo-1576678927484-cc907957088c?w=800&h=500&fit=crop&q=80",
      description: "Cross-platform mobile app for fitness tracking and wellness.",
      technologies: ["React Native", "Firebase", "Node.js"],
      client: "FitLife Inc",
      duration: "3 months",
      link: "#"
    },
    {
      id: 4,
      title: "Cybersecurity Dashboard",
      category: "security",
      image: "https://images.unsplash.com/photo-**********-ef010cbdcc31?w=800&h=500&fit=crop&q=80",
      description: "Real-time security monitoring and threat detection system.",
      technologies: ["Vue.js", "Python", "ElasticSearch"],
      client: "SecureNet",
      duration: "5 months",
      link: "#"
    },
    {
      id: 5,
      title: "Smart Home Automation",
      category: "enterprise",
      image: "https://images.unsplash.com/photo-1558002038-1055907df827?w=800&h=500&fit=crop&q=80",
      description: "IoT-based home automation system with AI capabilities.",
      technologies: ["IoT", "Python", "TensorFlow", "React"],
      client: "SmartHome Co",
      duration: "4 months",
      link: "#"
    },
    {
      id: 6,
      title: "Educational Platform",
      category: "web",
      image: "https://images.unsplash.com/photo-1501504905252-473c47e087f8?w=800&h=500&fit=crop&q=80",
      description: "Online learning platform with interactive courses.",
      technologies: ["Next.js", "GraphQL", "MongoDB"],
      client: "EduTech Solutions",
      duration: "3 months",
      link: "#"
    }
  ];

  const filteredProjects = activeFilter === 'all' 
    ? projects 
    : projects.filter(project => project.category === activeFilter);

  return (
    
    <div className="min-h-screen bg-gray-50">
       <Helmet>
             <title>Portfolio-Sipher Web</title>
      </Helmet>
      {/* Hero Section */}
      <section className="relative pt-32 pb-20 overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 bg-gradient-to-b from-blue-50 via-white to-transparent"></div>
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 1px 1px, #100562 1px, transparent 0)`,
          backgroundSize: '48px 48px',
          opacity: '0.05'
        }}></div>

        <div className="container mx-auto px-4 relative">
          <div className="max-w-5xl mx-auto text-center">
            {/* Portfolio Badge */}
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-blue-50 rounded-full mb-8">
              <Code2 className="w-5 h-5 text-[#100562]" />
              <span className="text-sm font-semibold text-[#100562]">Our Work</span>
            </div>

            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 mb-6">
              Transforming Ideas into
              <span className="relative block mt-2">
                Digital Excellence
                <div className="absolute left-0 -bottom-2 w-full h-2 bg-[#FF9B00] transform -skew-x-12 opacity-80"></div>
              </span>
            </h1>

            <p className="text-xl text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed">
              Explore our portfolio of successful projects that showcase our expertise
              in delivering innovative digital solutions across various industries.
            </p>

            {/* Quick Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-3xl mx-auto">
              <div className="bg-white p-6 rounded-xl shadow-lg">
                <div className="text-3xl font-bold text-[#100562] mb-2">500+</div>
                <div className="text-gray-600">Projects Delivered</div>
              </div>
              <div className="bg-white p-6 rounded-xl shadow-lg">
                <div className="text-3xl font-bold text-[#100562] mb-2">50+</div>
                <div className="text-gray-600">Industries Served</div>
              </div>
              <div className="bg-white p-6 rounded-xl shadow-lg">
                <div className="text-3xl font-bold text-[#100562] mb-2">98%</div>
                <div className="text-gray-600">Client Satisfaction</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Portfolio Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          {/* Category Filters */}
          <div className="flex flex-wrap justify-center gap-4 mb-12">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setActiveFilter(category.id)}
                className={`flex items-center gap-2 px-6 py-3 rounded-xl transition-all duration-300 ${
                  activeFilter === category.id
                    ? 'bg-[#100562] text-white shadow-lg'
                    : 'bg-white text-gray-700 hover:bg-gray-50'
                }`}
              >
                {category.icon && <category.icon className="w-5 h-5" />}
                {category.label}
              </button>
            ))}
          </div>

          {/* Projects Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredProjects.map((project) => (
              <motion.div
                key={project.id}
                layout
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 20 }}
                transition={{ duration: 0.3 }}
                className="group bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300"
              >
                {/* Project Image */}
                <div className="relative h-48 overflow-hidden">
                  <img
                    src={project.image}
                    alt={project.title}
                    className="w-full h-full object-cover transform group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  <a
                    href={project.link}
                    className="absolute bottom-4 right-4 bg-white/90 backdrop-blur-sm rounded-full p-2 opacity-0 group-hover:opacity-100 transition-all duration-300 hover:bg-white"
                  >
                    <ExternalLink className="w-5 h-5 text-[#100562]" />
                  </a>
                </div>

                {/* Project Details */}
                <div className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-[#100562] transition-colors duration-300">
                    {project.title}
                  </h3>
                  
                  <p className="text-gray-600 mb-4">{project.description}</p>

                  {/* Project Info */}
                  <div className="space-y-2 mb-4">
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Shield className="w-4 h-4 text-[#100562]" />
                      <span>Client: {project.client}</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <Sparkles className="w-4 h-4 text-[#100562]" />
                      <span>Duration: {project.duration}</span>
                    </div>
                  </div>

                  {/* Technologies */}
                  <div className="flex flex-wrap gap-2">
                    {project.technologies.map((tech, index) => (
                      <span
                        key={index}
                        className="text-sm bg-blue-50 text-[#100562] px-3 py-1 rounded-full"
                      >
                        {tech}
                      </span>
                    ))}
                  </div>
                </div>
              </motion.div>
            ))}
          </div>

          {/* Load More Button */}
          <div className="text-center mt-12">
            <button className="inline-flex items-center gap-2 px-8 py-4 bg-[#100562] text-white rounded-xl hover:bg-blue-700 transition-all duration-300">
              Load More Projects
              <ArrowRight className="w-5 h-5" />
            </button>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-[#100562] relative overflow-hidden">
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0" style={{
            backgroundImage: `radial-gradient(circle at 1px 1px, white 1px, transparent 0)`,
            backgroundSize: '24px 24px'
          }}></div>
        </div>

        <div className="container mx-auto px-4 relative">
          <div className="max-w-4xl mx-auto text-center">
            <h2 className="text-4xl font-bold text-white mb-6">
              Ready to Start Your Project?
              <span className="relative block mt-2">
                Let's Create Together
                <div className="absolute left-0 -bottom-2 w-full h-2 bg-[#FF9B00] transform -skew-x-12 opacity-80"></div>
              </span>
            </h2>

            <p className="text-xl text-blue-200 mb-12 leading-relaxed">
              Transform your vision into reality with our expert team of developers,
              designers, and strategists.
            </p>

            <div className="flex flex-col sm:flex-row items-center justify-center gap-6">
              <a
                href="/contact"
                className="group px-8 py-4 bg-white text-[#100562] rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1"
              >
                <span className="flex items-center gap-2">
                  Start Your Project
                  <ArrowRight className="w-5 h-5 group-hover:translate-x-1 transition-transform duration-300" />
                </span>
              </a>
              <a
                href="/services"
                className="group px-8 py-4 bg-white/10 text-white rounded-xl hover:bg-white/20 transition-all duration-300 transform hover:-translate-y-1"
              >
                <span className="flex items-center gap-2">
                  Explore Services
                  <Sparkles className="w-5 h-5" />
                </span>
              </a>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Portfolio;