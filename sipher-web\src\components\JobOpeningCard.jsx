import React from 'react';
import { FaChevronRight } from 'react-icons/fa';

const JobOpeningCard = ({ job }) => {
  return (
    <div className="bg-white p-8 rounded-3xl shadow-2xl transform hover:scale-105 hover:shadow-lg transition-all duration-300 ease-out">
      <h3 className="text-3xl font-extrabold text-gray-900 mb-4">{job.title}</h3>
      <p className="text-lg text-gray-600 mb-2"><strong>Location:</strong> {job.location}</p>
      <p className="text-lg text-gray-600 mb-2"><strong>Type:</strong> {job.type}</p>
      <p className="text-lg text-gray-600 mb-6">{job.description}</p>
      <button className="bg-[#FF9B00] text-white px-6 py-3 rounded-full hover:bg-[#100562] transform hover:scale-110 transition-all flex items-center justify-center space-x-2">
        <a href="https://forms.gle/Sqb7pr2SW31C29uJ7"><span>Apply Now</span></a> <FaChevronRight />
      </button>
    </div>
  );
};

export default JobOpeningCard;
