import React from 'react';
import { FaHandshake, FaLightbulb, FaUsers, FaChartLine, FaHeart, FaLeaf } from 'react-icons/fa';

const CoreValues = () => (
  <section className="bg-white py-16 text-gray-800">
    <div className="container mx-auto px-6 md:px-12 lg:px-20">
      {/* Heading */}
      <h2 className="text-2xl sm:text-3xl md:text-4xl font-bold text-center mb-12 text-[#100562]">
        Our Core Values
        <span className="block w-20 h-1 bg-[#FF9B00] mt-2 mx-auto rounded-full"></span>
      </h2>

      {/* Core Values Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-12">
        {[
          { icon: <FaHandshake className="text-[#FF9B00]" />, title: 'Integrity', description: 'We uphold the highest standards of integrity, ensuring honesty and transparency in all we do.' },
          { icon: <FaLightbulb className="text-[#FF9B00]" />, title: 'Innovation', description: 'We embrace innovation, constantly finding new ways to solve challenges and deliver value.' },
          { icon: <FaUsers className="text-[#FF9B00]" />, title: 'Collaboration', description: 'Collaboration drives our success. We work closely with our clients and teams to achieve shared goals.' },
          { icon: <FaChartLine className="text-[#FF9B00]" />, title: 'Excellence', description: 'We strive for excellence in every aspect, delivering solutions that exceed expectations.' },
          { icon: <FaHeart className="text-[#FF9B00]" />, title: 'Passion', description: 'Passion fuels our commitment to delivering exceptional results in every project.' },
          { icon: <FaLeaf className="text-[#FF9B00]" />, title: 'Sustainability', description: 'We are committed to sustainability, crafting solutions that positively impact both our clients and the environment.' },
        ].map((value, index) => (
          <div
            key={index}
            className="value-box text-center p-8 bg-gray-100 rounded-lg shadow-lg transition duration-300 hover:shadow-xl"
          >
            <div className="text-4xl mb-4">{value.icon}</div>
            <h3 className="text-2xl font-bold mb-2">{value.title}</h3>
            <p className="text-base">{value.description}</p>
          </div>
        ))}
      </div>
    </div>
  </section>
);

export default CoreValues;
