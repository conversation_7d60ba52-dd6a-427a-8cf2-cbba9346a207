import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>rk<PERSON>, <PERSON> } from 'lucide-react';

const ProductHero = () => {
  return (
    <section className="relative pt-24 pb-16 overflow-hidden bg-gray-50">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-b from-blue-50 via-white to-transparent"></div>
      <div
        className="absolute inset-0"
        style={{
          backgroundImage: `radial-gradient(circle at 1px 1px, #100562 1px, transparent 0)`,
          backgroundSize: '36px 36px',
          opacity: '0.05',
        }}
      ></div>

      <div className="container mx-auto px-4 relative">
        <div className="max-w-4xl mx-auto text-center">
          {/* Product Badge */}
          <div className="inline-flex items-center gap-2 px-3 py-1 bg-blue-50 rounded-full mb-6">
            <Shield className="w-4 h-4 text-[#100562]" />
            <span className="text-xs font-semibold text-[#100562]">Enterprise Solutions</span>
          </div>

          {/* Main Title */}
          <h1 className="text-3xl md:text-5xl font-bold text-gray-900 mb-4">
            Transform Your Business with
            <span className="relative block mt-2">
              Premium Solutions
              <div className="absolute left-0 -bottom-1 w-full h-1 bg-[#FF9B00] transform -skew-x-12 opacity-80"></div>
            </span>
          </h1>

          <p className="text-base md:text-lg text-gray-600 mb-8 max-w-2xl mx-auto leading-relaxed">
            Discover our comprehensive suite of enterprise solutions designed to elevate your business
            operations and drive exceptional growth.
          </p>

          {/* Featured Product Preview */}
          <div className="relative mb-8">
            <img
              src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=1200&h=600&fit=crop&q=80"
              alt="Enterprise Solutions"
              className="rounded-xl shadow-lg"
            />
            <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-full px-3 py-1 flex items-center gap-2">
              <Star className="w-4 h-4 text-yellow-500" />
              <span className="text-xs font-semibold text-gray-800">Featured Product</span>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-8">
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <div className="text-2xl font-bold text-[#100562] mb-1">99.9%</div>
              <div className="text-sm text-gray-600">Uptime Guaranteed</div>
            </div>
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <div className="text-2xl font-bold text-[#100562] mb-1">24/7</div>
              <div className="text-sm text-gray-600">Premium Support</div>
            </div>
            <div className="bg-white p-4 rounded-lg shadow-sm">
              <div className="text-2xl font-bold text-[#100562] mb-1">500+</div>
              <div className="text-sm text-gray-600">Enterprise Clients</div>
            </div>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
            <a
              href="#productlist"
              className="group px-6 py-3 bg-[#100562] text-white rounded-lg shadow-md hover:shadow-lg transition-transform duration-300 transform hover:-translate-y-1 focus:outline-none focus:ring-4 focus:ring-blue-300"
            >
              <span className="flex items-center gap-2">
                Explore Products
                <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
              </span>
            </a>
            <a
              href="#demo"
              className="group px-6 py-3 bg-white text-[#100562] rounded-lg border border-[#100562] hover:bg-blue-50 transition-transform duration-300 transform hover:-translate-y-1 focus:outline-none focus:ring-4 focus:ring-blue-100"
            >
              <span className="flex items-center gap-2">
                Request Demo
                <Sparkles className="w-4 h-4" />
              </span>
            </a>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ProductHero;
