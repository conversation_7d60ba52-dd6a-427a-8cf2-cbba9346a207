import React from 'react';
import { ArrowR<PERSON>, Star, Shield, Zap } from 'lucide-react';

const FeaturedProducts = () => {
    const products = [
        {
          title: "School Management Software",
          description: "Efficient management of student records, attendance, and administration.",
          image: "https://plus.unsplash.com/premium_photo-1663050763436-818382a24bb8?q=80&w=1470&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
          price: "₹9,999",
          features: ["Student database", "Attendance tracking", "Report generation", "Fee management"],
          badge: "Popular"
        },
        {
          title: "Hospital Management Software",
          description: "Comprehensive software for managing patient records, appointments, and billing.",
          image: "https://img.freepik.com/premium-photo/world-global-healthcare-ecology_117856-382.jpg?w=740",
          price: "₹12,999",
          features: ["Patient management", "Billing system", "Doctor scheduling", "Inventory management"],
          badge: "Best Seller"
        },
        {
          title: "Pathology Software",
          description: "Simplified software for managing pathology labs and generating reports.",
          image: "https://img.freepik.com/free-photo/equipment-researchers_23-**********.jpg?t=st=**********~exp=**********~hmac=d7282df9319ee276dcbe23b6fcf251a302d5b4352bd6e2c1477827db9f11d3de&w=740",
          price: "₹8,999",
          features: ["Lab test management", "Automated report generation", "Inventory tracking"],
          badge: "New"
        },
        {
          title: "Billing Software Development",
          description: "Customizable billing software for businesses of all sizes.",
          image: "https://img.freepik.com/free-photo/invoice-billing-information-form-graphic-concept_53876-132662.jpg?t=st=**********~exp=**********~hmac=93f27f50fd202097f029bccf7a5a88eedbb741c90534b93149502b514fcddfad&w=740",
          price: "₹5,999",
          features: ["GST integration", "Invoice generation", "Customer tracking"],
          badge: "Affordable"
        },
        {
          title: "Hostel Management Software",
          description: "Streamlined software for managing hostels, residents, and payments.",
          image: "https://img.freepik.com/premium-photo/hostel-dorm-room-with-beds-arranged-modern-design-double-single-beds-with-bunk-beds_1268487-7426.jpg?w=740",
          price: "₹44,999",
          features: ["Resident records", "Room allocation", "Payment tracking", "Maintenance management"],
          badge: "Premium"
        },
        {
          title: "Tiffin Services Management Software",
          description: "Automated software for managing tiffin services and subscriptions.",
          image: "https://img.freepik.com/premium-photo/indian-street-eats-chaat-magic-indian-street-food-chaat-image-photography_1020697-171146.jpg?w=740",
          price: "₹4,999",
          features: ["Order tracking", "Customer database", "Subscription management"],
          badge: "Affordable"
        },
        {
          title: "Real Estate Management Software",
          description: "Simplify property listings, client management, and transactions.",
          image: "https://img.freepik.com/free-photo/hotel_1127-4035.jpg?t=st=1737372581~exp=1737376181~hmac=994f3b93eed53862d2970e3781105d2d2d05abb5ae5f47b50b9d3f3e9ae4b047&w=740",
          price: "₹16,999",
          features: ["Property listing", "Client management", "Transaction tracking"],
          badge: "Popular"
        },
        {
          title: "Bricks Management Software",
          description: "Efficient software for managing brick production and inventory.",
          image: "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcR8brTbYKHw7ltgrr0ilZ_duOB0HdjtKkBBeA&s",
          price: "₹12,999",
          features: ["Inventory management", "Production tracking", "Order management"],
          badge: "New"
        },
        {
          title: "Logo and Banners Design",
          description: "Custom logos and banners to enhance your brand identity.",
          image: "https://img.freepik.com/premium-vector/abstract-fluid-banner-template_544391-536.jpg?w=740",
          price: "₹2,000",
          features: ["Custom designs", "Unlimited revisions", "High-resolution files"],
          badge: "Creative"
        },
        {
          title: "Content Writing",
          description: "Professional content writing services for websites and blogs.",
          image: "https://images.unsplash.com/photo-1501504905252-473c47e087f8?q=80&w=1374&auto=format&fit=crop&ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D",
          price: "₹2/Word",
          features: ["SEO-friendly content", "Plagiarism-free", "Fast delivery"],
          badge: "Affordable"
        },
        {
          title: "Domain and Hosting Provider",
          description: "Reliable domain and hosting solutions for your website.",
          image: "https://whoapi.com/wp-content/uploads/2024/02/How-to-get-web-hosting-clients.jpg",
          price: "₹3,000/Year",
          features: ["Fast hosting", "Free SSL", "99.9% uptime"],
          badge: "Best Seller"
        },
        {
          title: "CRM and ERM Provider",
          description: "Advanced CRM and ERM solutions for business efficiency.",
          image: "https://img.freepik.com/free-vector/gradient-crm-illustration_23-**********.jpg?t=st=**********~exp=**********~hmac=0f852341e4e20f187ee80078ce1da2a8b85a887fb46c813bee9b5462ad02e6eb&w=740",
          price: "₹14,999",
          features: ["Client management", "Data analytics", "Custom reports"],
          badge: "Popular"
        },
        {
          title: "MLM Website and App Development",
          description: "Customized MLM platforms for businesses.",
          image: "https://images.unsplash.com/photo-**********-4b46a572b786?w=800&h=500&fit=crop&q=80",
          price: "₹9,999",
          features: ["Referral system", "User management", "Automated payouts"],
          badge: "Affordable"
        },
        {
          title: "E-Commerce Development",
          description: "Full-fledged e-commerce platform development for online businesses.",
          image: "https://img.freepik.com/free-photo/technology-app-development-wireless-e-commerce_53876-120300.jpg?t=st=1737373917~exp=1737377517~hmac=cafe7b8773f84857ff3c3460bbb177581f591fe83d6b5abf01acf8f574066878&w=740",
          price: "₹15,999",
          features: ["Product catalog", "Secure payments", "Order tracking"],
          badge: "Best Seller"
        },
        {
          title: "Taxi Booking App Development",
          description: "User-friendly app for taxi services.",
          image: "https://images.unsplash.com/photo-1544476915-ed1370594142?w=800&h=500&fit=crop&q=80",
          price: "₹49,000",
          features: ["Live tracking", "Driver management", "Payment integration"],
          badge: "Premium"
        },
        {
          title: "Food Delivery App",
          description: "Streamlined app for food delivery services.",
          image: "https://img.freepik.com/premium-photo/cans-preserves-wooden-table_123827-14018.jpg?ga=GA1.1.1136222571.1707128225&semt=ais_hybrid",
          price: "₹49,999",
          features: ["Menu management", "Order tracking", "Payment gateway"],
          badge: "New"
        },
        {
          title: "Tournament and Contest App Development",
          description: "App for managing tournaments and contests seamlessly.",
          image: "https://images.unsplash.com/photo-1593642532973-d31b6557fa68?w=800&h=500&fit=crop&q=80",
          price: "₹49,999",
          features: ["Event scheduling", "Leaderboard", "Live updates"],
          badge: "Premium"
        },
        {
          title: "Education Portals Development",
          description: "Customized portals for online education and learning.",
          image: "https://images.unsplash.com/photo-1522071820081-009f0129c71c?w=800&h=500&fit=crop&q=80",
          price: "₹79,999",
          features: ["Online courses", "Student management", "Certification generation"],
          badge: "Popular"
        },
      ];
      

  return (
    <section id="productlist" className="py-20 bg-gradient-to-b from-white to-gray-50">
      <div className="container mx-auto px-4">
        <div className="text-center mb-16">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Featured
            <span className="relative ml-2">
              Products
              <div className="absolute left-0 -bottom-2 w-full h-1 bg-[#FF9B00] transform -skew-x-12 opacity-80"></div>
            </span>
          </h2>
          <p className="text-base text-gray-600 max-w-2xl mx-auto">
            Discover our most popular enterprise solutions trusted by industry leaders worldwide.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {products.map((product, index) => (
            <div
              key={index}
              className="group bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300"
            >
              {/* Product Image */}
              <div className="relative h-48 overflow-hidden">
                <img
                  src={product.image}
                  alt={product.title}
                  className="w-full h-full object-cover transform group-hover:scale-110 transition-transform duration-500"
                />
                <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-full px-3 py-1 flex items-center gap-2">
                  <Star className="w-4 h-4 text-yellow-500 fill-current" />
                  <span className="text-sm font-medium">{product.badge}</span>
                </div>
              </div>

              {/* Product Details */}
              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-[#100562] transition-colors duration-300">
                  {product.title}
                </h3>
                
                <p className="text-gray-600 mb-6">{product.description}</p>

                {/* Features List */}
                <ul className="space-y-3 mb-6">
                  {product.features.map((feature, i) => (
                    <li key={i} className="flex items-center gap-2 text-gray-600">
                      <Shield className="w-4 h-4 text-[#100562]" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>

                {/* Price and CTA */}
                <div className="flex items-center justify-between">
                  <div className="text-[#100562] font-semibold">{product.price}</div>
                  <a
                    href="#learn-more"
                    className="inline-flex items-center gap-2 text-[#100562] font-medium hover:gap-3 transition-all duration-300"
                  >
                    Learn More
                    <ArrowRight className="w-5 h-5" />
                  </a>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Bottom CTA */}
        <div className="text-center mt-12">
          <a
            href="#all-products"
            className="inline-flex items-center gap-2 px-6 py-3 bg-[#100562] text-white rounded-xl hover:bg-blue-700 transition-all duration-300"
          >
            View All Products
            <Zap className="w-5 h-5" />
          </a>
        </div>
      </div>
    </section>
  );
};

export default FeaturedProducts;