import React from 'react';
import projectImage1 from '../assets/project1.png';
import projectImage2 from '../assets/project2.png';
import projectImage3 from '../assets/project3.jpg';

const projects = [
  {
    id: 1,
    title: 'E-Commerce Website',
    description: 'An interactive and scalable Traval package platform.',
    imageUrl: [projectImage1],
    url: 'https://himviewtourism.com/',
  },
  {
    id: 2,
    title: 'Portfolio Website',
    description: 'A professional portfolio to showcase creative work.',
    imageUrl: [projectImage2],
    url: 'https://appworkstechnologies.in/',
  },
  {
    id: 3,
    title: 'Product showcase website',
    description: 'A website to showcase products and services.',
    imageUrl: [projectImage3],
    url: 'https://www.steamaxburner.com/',
  },
  {
    id: 4,
    title: 'Business Landing Page',
    description: 'A landing page for a tech startup with engaging UI/UX.',
    imageUrl: '/images/project4.jpg',
    url: 'https://example.com/business-landing-page',
  },
];

const ProjectShowcaseGrid = () => {
  return (
    <section className="px-6 py-10 md:px-12 lg:px-16 bg-white">
      <h2 className="text-3xl md:text-4xl font-bold text-center text-[#100562] mb-8 relative">
        Our Projects
        <span className="block w-24 h-1 bg-[#FF9B00] absolute left-1/2 transform -translate-x-1/2 mt-2"></span>
      </h2>

      <div className="grid gap-8 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        {projects.map((project) => (
          <a
            key={project.id}
            href={project.url}
            target="_blank"
            rel="noopener noreferrer"
            className="relative group bg-gray-100 rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300"
          >
            <img
              src={project.imageUrl}
              alt={project.title}
              className="w-full h-30 object-cover group-hover:scale-105 transition-transform duration-300"
            />

            <div className="p-4">
              <h3 className="text-lg font-semibold text-[#100562]">{project.title}</h3>
              <p className="text-sm text-gray-700">{project.description}</p>
            </div>
            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-25 transition duration-300"></div>
          </a>
        ))}
      </div>
    </section>
  );
};

export default ProjectShowcaseGrid;
