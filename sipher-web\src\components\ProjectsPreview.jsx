import React from 'react';
import { <PERSON>R<PERSON>, Code2, Globe2, <PERSON>, <PERSON>rk<PERSON> } from 'lucide-react';

const PortfolioSection = () => {
  const projects = [
    {
      title: "E-Commerce Platform",
      category: "Web Development",
      image: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=500&fit=crop&q=80",
      description: "Modern e-commerce solution with advanced features",
      technologies: ["React", "Node.js", "MongoDB"],
      link: "/portfolio/e-commerce"
    },
    {
      title: "Healthcare App",
      category: "Mobile Development",
      image: "https://images.unsplash.com/photo-1576091160399-112ba8d25d1d?w=800&h=500&fit=crop&q=80",
      description: "Patient management and telehealth platform",
      technologies: ["React Native", "Firebase", "WebRTC"],
      link: "/portfolio/healthcare"
    },
    {
      title: "Analytics Dashboard",
      category: "Enterprise Solutions",
      image: "https://images.unsplash.com/photo-**********-bebda4e38f71?w=800&h=500&fit=crop&q=80",
      description: "Real-time data visualization and reporting",
      technologies: ["Vue.js", "D3.js", "Python"],
      link: "/portfolio/analytics"
    }
  ];

  return (
    <section className="py-12 md:py-16 bg-white">
      <div className="container mx-auto px-4">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-blue-50 rounded-full mb-6">
            <Code2 className="w-5 h-5 text-blue-600" />
            <span className="text-sm font-semibold text-blue-600">Featured Projects</span>
          </div>
          
          <h2 className="text-4xl font-bold text-gray-900 mb-6">
            Our Latest
            <span className="relative ml-2">
              Work
              <div className="absolute left-0 -bottom-2 w-full h-2 bg-[#FF9B00] transform -skew-x-12 opacity-80"></div>
            </span>
          </h2>
          
          <p className="text-lg text-gray-600 max-w-3xl mx-auto">
            Explore our portfolio of successful projects that showcase our expertise
            in delivering innovative digital solutions.
          </p>
        </div>

        {/* Projects Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {projects.map((project, index) => (
            <div
              key={index}
              className="group bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300"
            >
              {/* Project Image */}
              <div className="relative h-48 overflow-hidden">
                <img
                  src={project.image}
                  alt={project.title}
                  className="w-full h-full object-cover transform group-hover:scale-110 transition-transform duration-500"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <div className="absolute bottom-4 left-4 bg-white/90 backdrop-blur-sm rounded-full px-3 py-1 text-sm font-medium text-blue-600">
                  {project.category}
                </div>
              </div>

              {/* Project Details */}
              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors duration-300">
                  {project.title}
                </h3>
                
                <p className="text-gray-600 mb-4">{project.description}</p>

                {/* Technologies */}
                <div className="flex flex-wrap gap-2 mb-6">
                  {project.technologies.map((tech, i) => (
                    <span
                      key={i}
                      className="text-sm bg-blue-50 text-blue-600 px-3 py-1 rounded-full"
                    >
                      {tech}
                    </span>
                  ))}
                </div>

                <a
                  href={project.link}
                  className="inline-flex items-center gap-2 text-blue-600 font-medium hover:gap-3 transition-all duration-300"
                >
                  View Project
                  <ArrowRight className="w-5 h-5" />
                </a>
              </div>
            </div>
          ))}
        </div>

        {/* Stats Section */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16">
          {[
            { value: "500+", label: "Projects Completed" },
            { value: "150+", label: "Happy Clients" },
            { value: "10+", label: "Industries Served" },
            { value: "15+", label: "Years Experience" }
          ].map((stat, index) => (
            <div key={index} className="text-center">
              <div className="text-3xl font-bold text-blue-600 mb-2">{stat.value}</div>
              <div className="text-gray-600">{stat.label}</div>
            </div>
          ))}
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <a
            href="/portfolio"
            className="inline-flex items-center gap-2 px-8 py-4 bg-[#100562] text-white rounded-xl hover:bg-blue-700 transition-all duration-300"
          >
            View All Projects
            <ArrowRight className="w-5 h-5" />
          </a>
        </div>
      </div>
    </section>
  );
};

export default PortfolioSection;