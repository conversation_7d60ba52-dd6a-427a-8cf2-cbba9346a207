import React from 'react';
import { Shield, ArrowRight, Award, Globe2, Users2, Zap } from 'lucide-react';
import team1Image from '../assets/team.jpeg';
import team2Image from '../assets/team2.jpg';

const AboutUsPreview = () => {
  const achievements = [
    {
      icon: <Award className="w-6 h-6" />,
      title: "Excellence",
      value: "15+",
      label: "Years of Innovation"
    },
    {
      icon: <Globe2 className="w-6 h-6" />,
      title: "Global Impact",
      value: "500+",
      label: "Projects Delivered"
    },
    {
      icon: <Shield className="w-6 h-6" />,
      title: "Trust",
      value: "98%",
      label: "Client Satisfaction"
    }
  ];

  return (
    <section className="relative py-12 md:py-16 overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 via-white to-purple-50/20" />
      <div className="absolute inset-0" style={{
        backgroundImage: "radial-gradient(circle at 20px 20px, #f1f5f9 1px, transparent 0)",
        backgroundSize: "40px 40px",
        opacity: 0.3
      }} />

      <div className="container mx-auto px-4 relative">
        <div className="max-w-7xl mx-auto">
          {/* Two Column Layout */}
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            {/* Left Column - Content */}
            <div>
              <div className="inline-flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-50 to-purple-50 rounded-full border border-blue-100 shadow-sm mb-6">
                <Shield className="w-5 h-5 text-[#100562]" />
                <span className="text-sm font-semibold bg-gradient-to-r from-[#100562] to-purple-600 bg-clip-text text-transparent">
                  Trusted by Industry Leaders
                </span>
              </div>

              <h2 className="text-4xl font-bold text-gray-900 mb-6">
                Transforming Ideas into
                <span className="relative block mt-2">
                  Digital Excellence
                  <div className="absolute left-0 -bottom-2 w-full h-2 bg-[#FF9B00] transform -skew-x-12 opacity-80" />
                </span>
              </h2>

              <p className="text-lg text-gray-600 leading-relaxed mb-8">
                At Sipher Web, we craft exceptional digital experiences that empower businesses 
                to thrive in the digital age. Our passion for innovation and commitment to 
                excellence has made us a trusted partner for companies worldwide.
              </p>

              {/* Feature Grid */}
              <div className="grid grid-cols-2 gap-6 mb-8">
                {[
                  {
                    icon: <Users2 className="w-5 h-5" />,
                    title: "Expert Team",
                    desc: "Seasoned professionals"
                  },
                  {
                    icon: <Globe2 className="w-5 h-5" />,
                    title: "Global Reach",
                    desc: "Worldwide impact"
                  },
                  {
                    icon: <Shield className="w-5 h-5" />,
                    title: "Enterprise Grade",
                    desc: "Secure solutions"
                  },
                  {
                    icon: <Zap className="w-5 h-5" />,
                    title: "Fast Delivery",
                    desc: "Quick turnaround"
                  }
                ].map((feature, index) => (
                  <div key={index} className="flex items-start gap-3 p-4 bg-white rounded-xl shadow-sm hover:shadow-md transition-all duration-300">
                    <div className="p-2 bg-blue-50 rounded-lg text-[#100562]">
                      {feature.icon}
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{feature.title}</h3>
                      <p className="text-sm text-gray-600">{feature.desc}</p>
                    </div>
                  </div>
                ))}
              </div>

              <a
                href="/about"
                className="inline-flex items-center gap-2 text-[#100562] font-medium hover:gap-3 transition-all duration-300"
              >
                Learn More About Us
                <ArrowRight className="w-5 h-5" />
              </a>
            </div>

            {/* Right Column - Image Grid */}
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20 rounded-2xl blur-2xl" />
              <div className="relative grid grid-cols-2 gap-4">
                <div className="space-y-4">
                  <div className="rounded-2xl overflow-hidden shadow-lg">
                    <img
                      src={team1Image}
                      alt="Team collaboration"
                      className="w-full h-48 object-cover hover:scale-110 transition-transform duration-500 border-4 border-blue-500"
                    />
                  </div>
                  <div className="bg-white p-6 rounded-2xl shadow-lg">
                    <div className="text-3xl font-bold text-[#100562] mb-1">24/7</div>
                    <div className="text-sm text-gray-600">Expert Support</div>
                  </div>
                </div>
                <div className="space-y-4 pt-8">
                  <div className="bg-white p-6 rounded-2xl shadow-lg">
                    <div className="text-3xl font-bold text-[#100562] mb-1">50+</div>
                    <div className="text-sm text-gray-600">Tech Stack</div>
                  </div>
                  <div className="rounded-2xl overflow-hidden shadow-lg">
                    <img
                      src={team2Image}
                      alt="Team workspace"
                      className="w-full h-48 object-cover hover:scale-110 transition-transform duration-500 border-4 border-blue-500"
                    />
                  </div>
                </div>
              </div>

              {/* Trust Badge */}
              <div className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 bg-white px-6 py-3 rounded-full shadow-lg border border-gray-100">
                <div className="flex items-center gap-3 text-[#100562]">
                  <Shield className="w-5 h-5" />
                  <span className="font-medium whitespace-nowrap">ISO 27001 Certified</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutUsPreview;