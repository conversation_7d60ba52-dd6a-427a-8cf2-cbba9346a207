import React from 'react';
import { Message<PERSON>ircle, ThumbsUp, <PERSON>, ArrowR<PERSON>, Users2, MessageSquare } from 'lucide-react';

const ForumSection = () => {
  const discussions = [
    {
      title: "Best practices for React performance optimization",
      author: {
        name: "<PERSON>",
        avatar: "https://images.unsplash.com/photo-1494790108377-be9c29b29330?w=100&h=100&fit=crop&q=80"
      },
      category: "Development",
      replies: 45,
      views: 1200,
      likes: 89,
      isHot: true
    },
    {
      title: "Understanding microservices architecture",
      author: {
        name: "<PERSON>",
        avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&q=80"
      },
      category: "Architecture",
      replies: 32,
      views: 850,
      likes: 67,
      isHot: false
    },
    {
      title: "UI/UX design trends for 2024",
      author: {
        name: "<PERSON>",
        avatar: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100&h=100&fit=crop&q=80"
      },
      category: "Design",
      replies: 56,
      views: 1500,
      likes: 120,
      isHot: true
    }
  ];

  return (
    <section className="py-20 bg-gray-50">
      <div className="container mx-auto px-4">
        <div className="max-w-7xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-blue-50 rounded-full mb-6">
              <MessageSquare className="w-5 h-5 text-[#100562]" />
              <span className="text-sm font-semibold text-[#100562]">Community Forums</span>
            </div>
            
            <h2 className="text-4xl font-bold text-gray-900 mb-6">
              Join the
              <span className="relative ml-2">
                Discussion
                <div className="absolute left-0 -bottom-2 w-full h-2 bg-[#FF9B00] transform -skew-x-12 opacity-80"></div>
              </span>
            </h2>
            
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Engage with our community in meaningful discussions, share your knowledge,
              and learn from others' experiences.
            </p>
          </div>

          {/* Forum Categories */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
            {[
              { icon: <MessageCircle />, name: "General Discussion", count: "2.5k topics" },
              { icon: <Users2 />, name: "Help & Support", count: "1.8k topics" },
              { icon: <ThumbsUp />, name: "Showcase", count: "950 topics" }
            ].map((category, index) => (
              <div
                key={index}
                className="bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-all duration-300"
              >
                <div className="flex items-center gap-4">
                  <div className="p-3 bg-blue-50 rounded-lg text-[#100562]">
                    {category.icon}
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">{category.name}</h3>
                    <p className="text-sm text-gray-500">{category.count}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Recent Discussions */}
          <div className="bg-white rounded-2xl shadow-lg overflow-hidden">
            <div className="p-6 border-b border-gray-100">
              <h3 className="text-xl font-semibold text-gray-900">Recent Discussions</h3>
            </div>

            <div className="divide-y divide-gray-100">
              {discussions.map((discussion, index) => (
                <div key={index} className="p-6 hover:bg-gray-50 transition-colors duration-300">
                  <div className="flex items-start gap-4">
                    <img
                      src={discussion.author.avatar}
                      alt={discussion.author.name}
                      className="w-10 h-10 rounded-full"
                    />
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <span className="px-3 py-1 bg-blue-50 text-[#100562] text-sm font-medium rounded-full">
                          {discussion.category}
                        </span>
                        {discussion.isHot && (
                          <span className="px-3 py-1 bg-red-50 text-red-600 text-sm font-medium rounded-full">
                            Hot 🔥
                          </span>
                        )}
                      </div>
                      
                      <h4 className="text-lg font-semibold text-gray-900 hover:text-[#100562] transition-colors duration-300">
                        {discussion.title}
                      </h4>
                      
                      <div className="flex items-center gap-6 mt-4 text-sm text-gray-500">
                        <div className="flex items-center gap-2">
                          <MessageCircle className="w-4 h-4" />
                          <span>{discussion.replies} replies</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Eye className="w-4 h-4" />
                          <span>{discussion.views} views</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <ThumbsUp className="w-4 h-4" />
                          <span>{discussion.likes} likes</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="p-6 bg-gray-50 border-t border-gray-100">
              <a
                href="#view-all"
                className="inline-flex items-center gap-2 text-[#100562] font-medium hover:gap-3 transition-all duration-300"
              >
                View All Discussions
                <ArrowRight className="w-5 h-5" />
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}

export default ForumSection;