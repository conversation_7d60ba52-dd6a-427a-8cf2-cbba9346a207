import React from 'react';

const HeroSection = () => {
  return (
    <div className="relative text-center mb-2 py-20 bg-white">
      
      

      {/* Content */}
      <div className="relative z-10">
        <h1 className="text-5xl md:text-7xl font-extrabold text-[#100562] mb-6 tracking-tight leading-tight md:leading-snug drop-shadow-lg">
          Join Our Dynamic Team
        </h1>
        <p className="text-xl md:text-2xl text-[#100562] max-w-3xl mx-auto px-4 md:px-0 mb-8 leading-relaxed">
          Be a part of a talented team working on cutting-edge projects that inspire innovation. We’re always on the lookout for passionate individuals who want to grow and make an impact with us.
        </p>
        <a href="#openings">
          <button className="bg-[#FF9B00] text-white font-bold text-lg py-2 px-4 rounded-full shadow-lg hover:bg-[#100562] transition-all transform hover:scale-105">
            View Open Positions
          </button>
        </a>
      </div>
    </div>
  );
};

export default HeroSection;
