import React from 'react';
import { Link } from 'react-router-dom';
import {
  Linkedin,
  Instagram,
  MessageCircle,
  Mail,
  Phone,
  MapPin,
  Download,
  ArrowRight,
  Clock,
  Shield,
  Globe,
  ChevronUp
} from 'lucide-react';

const Footer = () => {
  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const currentYear = new Date().getFullYear();

  const [showScrollButton, setShowScrollButton] = React.useState(false);

  const handleScroll = () => {
    const footerElement = document.querySelector('footer');
    const footerPosition = footerElement.getBoundingClientRect();
    const windowHeight = window.innerHeight;

    if (footerPosition.top < windowHeight && footerPosition.bottom >= 0) {
      setShowScrollButton(true);
    } else {
      setShowScrollButton(false);
    }
  };

  React.useEffect(() => {
    window.addEventListener('scroll', handleScroll);
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  return (
    <footer className="relative bg-[#100562] text-white overflow-hidden">
      { /* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: `radial-gradient(circle at 1px 1px, white 1px, transparent 0)`,
          backgroundSize: '24px 24px'
        }}></div>
      </div>

      {/* Newsletter Section */}
      <div className="relative border-b border-white/10">
        <div className="container mx-auto px-4 py-12">
          <div className="flex flex-col lg:flex-row items-center justify-between gap-8">
            <div className="w-full lg:max-w-xl text-center lg:text-left">
              <h3 className="text-2xl font-bold mb-2">Stay Updated</h3>
              <p className="text-blue-200">Subscribe to our newsletter for the latest updates and insights.</p>
            </div>
            <div className="w-full lg:flex-1 max-w-md">
              <form className="flex flex-col sm:flex-row gap-2" onSubmit={(e) => e.preventDefault()}>
                <input
                  type="email"
                  placeholder="Enter your email"
                  className="flex-1 px-4 py-3 rounded-lg bg-white/10 border border-white/20 focus:border-white/40 focus:ring-2 focus:ring-white/20 transition-all duration-300 placeholder-blue-200"
                />
                <button className="px-6 py-3 bg-[#FF9B00] text-[#100562] rounded-lg font-semibold hover:bg-white transition-all duration-300 whitespace-nowrap">
                  Subscribe
                </button>
              </form>
            </div>
          </div>
        </div>
      </div>

      {/* Main Footer Content */}
      <div className="relative container mx-auto px-4 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12">
          {/* Company Info */}
          <div className="space-y-6">
            <div>
              <h2 className="text-3xl font-bold text-white mb-4">
                Sipher Web Pvt Ltd
                <span className="text-[#FF9B00]">.</span>
              </h2>
              <p className="text-blue-200 leading-relaxed">
                Delivering excellence in web development and digital solutions. Transform your vision into reality with our innovative approach.
              </p>
            </div>

            {/* Download Brochure */}
            <div className="group">
              <a
                href="/SWCompany_Profile.pdf" 
                download
                className="inline-flex items-center gap-3 px-6 py-3 bg-white/10 hover:bg-white/20 rounded-xl transition-all duration-300"
              >
                <Download className="w-5 h-5 text-[#FF9B00]" />
                <span className="font-medium">Download Brochure</span>
                <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
              </a>
            </div>

            {/* Social Links */}
            <div className="flex gap-4">
              <a
                href="https://www.linkedin.com/company/sipher-web-private-limited/"
                target="_blank"
                rel="noopener noreferrer"
                className="p-3 bg-white/10 hover:bg-white/20 rounded-lg transition-all duration-300"
              >
                <Linkedin className="w-5 h-5" />
              </a>
              <a
                href="https://www.instagram.com/sipherweb/profilecard/?igsh=eHBlZXllcWs4cW56"
                target="_blank"
                rel="noopener noreferrer"
                className="p-3 bg-white/10 hover:bg-white/20 rounded-lg transition-all duration-300"
              >
                <Instagram className="w-5 h-5" />
              </a>
              <a
                href="https://wa.me/************"
                target="_blank"
                rel="noopener noreferrer"
                className="p-3 bg-white/10 hover:bg-white/20 rounded-lg transition-all duration-300"
              >
                <MessageCircle className="w-5 h-5" />
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-6">Quick Links</h3>
            <ul className="space-y-4">
              {[
                { label: 'About Us', path: '/about' },
                { label: 'Services', path: '/services' },
                { label: 'Portfolio', path: '/portfolio' },
                { label: 'Careers', path: '/career' },
                { label: 'Contact', path: '/contact' }
              ].map((link, index) => (
                <li key={index}>
                  <Link
                    to={link.path}
                    onClick={scrollToTop}
                    className="inline-flex items-center gap-2 text-blue-200 hover:text-white transition-colors duration-300"
                  >
                    <ArrowRight className="w-4 h-4" />
                    <span>{link.label}</span>
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-lg font-semibold mb-6">Contact Us</h3>
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <MapPin className="w-5 h-5 text-[#FF9B00] flex-shrink-0 mt-1" />
                <p className="text-blue-200">
                  First Floor, C-1 / 364, Near Muglai Hutz, Sector G, Jankipuram, Tedhi Pulia, Lucknow, India
                </p>
              </div>
              <a
                href="tel:+************"
                className="flex items-center gap-3 text-blue-200 hover:text-white transition-colors duration-300"
              >
                <Phone className="w-5 h-5 text-[#FF9B00]" />
                <span>+91 91255 45607</span>
              </a>
              <a
                href="mailto:<EMAIL>"
                className="flex items-center gap-3 text-blue-200 hover:text-white transition-colors duration-300"
              >
                <Mail className="w-5 h-5 text-[#FF9B00]" />
                <span><EMAIL></span>
              </a>
              <div className="flex items-center gap-3">
                <Clock className="w-5 h-5 text-[#FF9B00]" />
                <span className="text-blue-200">Mon - Sat: 9:00 AM - 6:00 PM</span>
              </div>
            </div>
          </div>

          {/* Map */}
          <div>
            <h3 className="text-lg font-semibold mb-6">Our Location</h3>
            <div className="rounded-xl overflow-hidden shadow-lg border border-white/10">
              <iframe
                src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3559.6177447311635!2d80.91713811503271!3d26.87697388314495!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x399bfd06b4c1d617%3A0x8129a5b18f2e65f!2sTedhi%20Pulia%2C%20Lucknow%2C%20Uttar%20Pradesh!5e0!3m2!1sen!2sin!4v1633106164780!5m2!1sen!2sin"
                width="100%"
                height="200"
                style={{ border: 0 }}
                allowFullScreen
                loading="lazy"
                title="Sipher Web Location"
              ></iframe>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="mt-16 pt-8 border-t border-white/10">
          <div className="flex flex-col md:flex-row items-center justify-between gap-4">
            <div className="flex items-center gap-6">
              <p className="text-blue-200 text-sm">
                © {currentYear} Sipher Web Pvt Ltd. All rights reserved.
              </p>
              <div className="hidden md:flex items-center gap-6">
                <Link to="/privacy" className="text-blue-200 hover:text-white text-sm transition-colors duration-300">
                  Privacy Policy
                </Link>
                <Link to="/terms" className="text-blue-200 hover:text-white text-sm transition-colors duration-300">
                  Terms of Service
                </Link>
                <Link to="/" className="text-blue-200 hover:text-white text-sm transition-colors duration-300">
                  Gateway
                </Link>
              </div>
            </div>
            <div className="flex items-center gap-4 text-blue-200">
              <div className="flex items-center gap-2">
                <Globe className="w-4 h-4" />
                <span className="text-sm">English</span>
              </div>
              <div className="flex items-center gap-2">
                <Shield className="w-4 h-4" />
                <span className="text-sm">ISO 27001 Certified</span>
              </div>
            </div>
          </div>
        </div>

        {/* Scroll to Top Button */}
        {showScrollButton && (
          <button
            onClick={scrollToTop}
            className="fixed bottom-8 right-8 p-3 bg-[#FF9B00] text-[#100562] rounded-xl shadow-lg hover:bg-white transition-all duration-300 z-50"
          >
            <ChevronUp className="w-6 h-6" />
          </button>
        )}
      </div>
    </footer>
  );
};

export default Footer;