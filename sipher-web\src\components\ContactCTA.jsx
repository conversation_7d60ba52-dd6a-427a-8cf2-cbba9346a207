// components/ContactCTA.jsx
import React from 'react';
import { ArrowRight, Phone, Mail, MapPin, MessageCircle } from 'lucide-react';
import teamImage from '../assets/team-3.jpeg';

const ContactCTA = () => {
  return (
    <section className="relative bg-gradient-to-t from-[#FFFEC5] to-[#fff] text-[#100562] px-6 py-12 md:py-16 lg:py-20 flex flex-col items-center text-center space-y-6 md:space-y-8">
      <h2 className="text-2xl md:text-3xl font-bold max-w-2xl">
        Ready to Discuss Your Next Project?
      </h2>
      <p className="text-lg md:text-xl font-light max-w-xl">
        Let’s bring your ideas to life. Reach out to us, and we’ll help create the best solution for your needs.
      </p>
      <a
        href="https://forms.gle/Sqb7pr2SW31C29uJ7"
        target="_blank"
        rel="noopener noreferrer"
        className="px-8 py-4 bg-[#100562] text-white rounded-lg font-semibold hover:bg-[#FFD200] hover:text-[#100562] transition duration-300"
      >
        Contact Us
      </a>
    </section>
  );
};

export default ContactCTA;
