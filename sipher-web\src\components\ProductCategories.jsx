import React from 'react';
import { Globe2, Shield, Cloud, Database, Code2, Smartphone } from 'lucide-react';

const ProductCategories = () => {
  const categories = [
    {
      icon: <Globe2 className="w-6 h-6" />,
      title: "Web Solutions",
      description: "Custom websites and web applications",
      count: "15+ Products",
    },
    {
      icon: <Shield className="w-6 h-6" />,
      title: "Security Solutions",
      description: "Enterprise-grade security systems",
      count: "8+ Products",
    },
    {
      icon: <Cloud className="w-6 h-6" />,
      title: "Cloud Services",
      description: "Scalable cloud infrastructure",
      count: "12+ Products",
    },
    {
      icon: <Database className="w-6 h-6" />,
      title: "Data Solutions",
      description: "Big data and analytics platforms",
      count: "10+ Products",
    },
    {
      icon: <Code2 className="w-6 h-6" />,
      title: "Custom Software",
      description: "Tailored software solutions",
      count: "20+ Products",
    },
    {
      icon: <Smartphone className="w-6 h-6" />,
      title: "Mobile Solutions",
      description: "iOS and Android applications",
      count: "14+ Products",
    },
  ];

  return (
    <section className="py-16 bg-white">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Explore Our
            <span className="relative ml-2">
              Categories
              <div className="absolute left-0 -bottom-1 w-full h-1 bg-[#FF9B00] transform -skew-x-12 opacity-80"></div>
            </span>
          </h2>
          <p className="text-base text-gray-600 max-w-2xl mx-auto">
            Browse through our comprehensive range of enterprise solutions designed to meet your specific business needs.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {categories.map((category, index) => (
            <div
              key={index}
              className="group bg-white p-6 rounded-xl shadow-md hover:shadow-lg transition-all duration-300 border border-gray-200 hover:border-[#100562]"
            >
              <div className="mb-4 p-3 bg-blue-50 rounded-xl w-fit group-hover:scale-105 transition-transform duration-300">
                <div className="text-[#100562]">{category.icon}</div>
              </div>

              <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-[#100562] transition-colors duration-300">
                {category.title}
              </h3>

              <p className="text-sm text-gray-600 mb-3">{category.description}</p>

              <div className="text-xs font-medium text-[#100562] bg-blue-50 px-3 py-1 rounded-full w-fit">
                {category.count}
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default ProductCategories;
