import React from 'react';
import { Users2, <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>rk<PERSON> } from 'lucide-react';

const JoinCommunity = () => {
  return (
    <section className="py-20 bg-gradient-to-b from-white to-gray-50">
      <div className="container mx-auto px-4">
        <div className="max-w-5xl mx-auto text-center">
          <div className="inline-flex items-center gap-2 px-4 py-2 bg-blue-50 rounded-full mb-8">
            <Users2 className="w-5 h-5 text-[#100562]" />
            <span className="text-sm font-semibold text-[#100562]">Join Today</span>
          </div>

          <h2 className="text-4xl font-bold text-gray-900 mb-6">
            Ready to Join Our
            <span className="relative ml-2">
              Community?
              <div className="absolute left-0 -bottom-2 w-full h-2 bg-[#FF9B00] transform -skew-x-12 opacity-80"></div>
            </span>
          </h2>

          <p className="text-lg text-gray-600 mb-12 max-w-3xl mx-auto">
            Join our thriving community of developers, designers, and innovators.
            Share knowledge, collaborate on projects, and grow together.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
            <div className="bg-white p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="w-16 h-16 bg-blue-50 rounded-2xl flex items-center justify-center text-[#100562] mx-auto mb-6">
                <Shield className="w-8 h-8" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4">Free Membership</h3>
              <ul className="space-y-4 text-left mb-8">
                {[
                  "Access to community forums",
                  "Join community events",
                  "Basic learning resources",
                  "Network with members"
                ].map((feature, index) => (
                  <li key={index} className="flex items-center gap-3">
                    <Sparkles className="w-5 h-5 text-[#100562]" />
                    <span className="text-gray-600">{feature}</span>
                  </li>
                ))}
              </ul>
              <a
                href="#join-free"
                className="inline-flex items-center justify-center w-full px-6 py-3 bg-gray-100 text-gray-900 rounded-xl hover:bg-gray-200 transition-all duration-300"
              >
                <span className="flex items-center gap-2">
                  Join Free
                  <ArrowRight className="w-5 h-5" />
                </span>
              </a>
            </div>

            <div className="bg-[#100562] p-8 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300">
              <div className="w-16 h-16 bg-white/10 rounded-2xl flex items-center justify-center text-[#FF9B00] mx-auto mb-6">
                <Shield className="w-8 h-8" />
              </div>
              <h3 className="text-xl font-bold text-white mb-4">Pro Membership</h3>
              <ul className="space-y-4 text-left mb-8">
                {[
                  "All Free features",
                  "Exclusive workshops & webinars",
                  "Premium learning resources",
                  "1-on-1 mentorship sessions",
                  "Priority support"
                ].map((feature, index) => (
                  <li key={index} className="flex items-center gap-3">
                    <Sparkles className="w-5 h-5 text-[#FF9B00]" />
                    <span className="text-blue-200">{feature}</span>
                  </li>
                ))}
              </ul>
              <a
                href="#join-pro"
                className="inline-flex items-center justify-center w-full px-6 py-3 bg-[#FF9B00] text-[#100562] rounded-xl hover:bg-white transition-all duration-300"
              >
                <span className="flex items-center gap-2">
                  Join Pro
                  <ArrowRight className="w-5 h-5" />
                </span>
              </a>
            </div>
          </div>

          <div className="text-center">
            <p className="text-sm text-gray-500">
              Have questions? <a href="#contact" className="text-[#100562] hover:underline">Contact our team</a>
            </p>
          </div>
        </div>
      </div>
    </section>
  );
}

export default JoinCommunity;