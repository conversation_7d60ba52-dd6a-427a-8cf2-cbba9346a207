"use client";
import { useState, useEffect, useRef } from "react";
import { motion } from "framer-motion";

const EnhancedCursor = () => {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [cursorType, setCursorType] = useState("default");
  const [isMobile, setIsMobile] = useState(false);
  const [isClicking, setIsClicking] = useState(false);
  const cursorRingRef = useRef(null);
  const cursorDotRef = useRef(null);
  const lastUpdateTime = useRef(0);
  const lastPosition = useRef({ x: 0, y: 0 });
  const velocity = useRef({ x: 0, y: 0 });

  // Check if device is mobile
  useEffect(() => {
    const checkIsMobile = () => {
      const userAgent = navigator.userAgent || window.opera;
      const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
      setIsMobile(/android|iphone|ipad|ipod|windows phone/i.test(userAgent) || isTouchDevice);
    };

    checkIsMobile();
    window.addEventListener("resize", checkIsMobile);
    return () => window.removeEventListener("resize", checkIsMobile);
  }, []);

  // Track mouse position and calculate velocity
  useEffect(() => {
    if (isMobile) return;

    const mouseMove = (e) => {
      const { clientX, clientY } = e;
      const now = performance.now();
      const dt = now - lastUpdateTime.current;

      if (dt > 0) {
        // Calculate velocity (pixels per millisecond)
        velocity.current = {
          x: (clientX - lastPosition.current.x) / dt,
          y: (clientY - lastPosition.current.y) / dt
        };

        lastPosition.current = { x: clientX, y: clientY };
        lastUpdateTime.current = now;
      }

      setMousePosition({ x: clientX, y: clientY });
    };

    window.addEventListener("mousemove", mouseMove);
    return () => window.removeEventListener("mousemove", mouseMove);
  }, [isMobile]);

  // Handle cursor interactions
  useEffect(() => {
    if (isMobile) return;

    // Define interactive elements
    const interactiveElements = [
      { selector: "a, button, [role=button], .clickable", type: "button" },
      { selector: "input, textarea, select, [contenteditable=true]", type: "input" },
      { selector: "img, video, canvas, .media", type: "media" },
      { selector: ".card, .hover-effect", type: "card" }
    ];

    // Handle mouse enter/leave
    const handleMouseEnter = (e, type) => {
      setCursorType(type);

      // Apply magnetic effect for buttons
      if (type === "button" && cursorRingRef.current) {
        e.currentTarget.classList.add("cursor-magnetic");
      }
    };

    const handleMouseLeave = (e) => {
      setCursorType("default");
      e.currentTarget.classList.remove("cursor-magnetic");
    };

    // Handle mouse down/up
    const handleMouseDown = () => setIsClicking(true);
    const handleMouseUp = () => setIsClicking(false);

    // Add event listeners
    const cleanupFunctions = interactiveElements.map(({ selector, type }) => {
      const elements = document.querySelectorAll(selector);

      elements.forEach(el => {
        const enterHandler = (e) => handleMouseEnter(e, type);
        const leaveHandler = (e) => handleMouseLeave(e);

        el.addEventListener("mouseenter", enterHandler);
        el.addEventListener("mouseleave", leaveHandler);
        el.style.cursor = "none";
      });

      return () => {
        elements.forEach(el => {
          const enterHandler = (e) => handleMouseEnter(e, type);
          const leaveHandler = (e) => handleMouseLeave(e);

          el.removeEventListener("mouseenter", enterHandler);
          el.removeEventListener("mouseleave", leaveHandler);
        });
      };
    });

    // Global mouse events
    document.addEventListener("mousedown", handleMouseDown);
    document.addEventListener("mouseup", handleMouseUp);
    document.body.style.cursor = "none";

    return () => {
      cleanupFunctions.forEach(cleanup => cleanup());
      document.removeEventListener("mousedown", handleMouseDown);
      document.removeEventListener("mouseup", handleMouseUp);
    };
  }, [isMobile]);

  // Apply magnetic effect to buttons
  useEffect(() => {
    if (isMobile) return;

    const handleMagneticMove = (e) => {
      const magneticElements = document.querySelectorAll(".cursor-magnetic");

      magneticElements.forEach(el => {
        const rect = el.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;

        // Calculate distance between cursor and element center
        const distanceX = centerX - e.clientX;
        const distanceY = centerY - e.clientY;
        const distance = Math.sqrt(distanceX * distanceX + distanceY * distanceY);

        // Apply magnetic effect if cursor is close enough
        if (distance < 100) {
          const strength = 0.3; // Adjust magnetic strength (0-1)
          el.style.transform = `translate(${-distanceX * strength}px, ${-distanceY * strength}px)`;
        } else {
          el.style.transform = "";
        }
      });
    };

    window.addEventListener("mousemove", handleMagneticMove);
    return () => window.removeEventListener("mousemove", handleMagneticMove);
  }, [isMobile]);

  // Don't render cursor on mobile devices
  if (isMobile) return null;

  // Calculate cursor size and opacity based on velocity
  const speed = Math.sqrt(velocity.current.x * velocity.current.x + velocity.current.y * velocity.current.y);
  const normalizedSpeed = Math.min(speed * 5, 1); // Normalize and clamp speed value

  // Calculate trail positions
  const trailCount = 3;
  const trails = Array.from({ length: trailCount }, (_, i) => {
    const factor = (i + 1) / (trailCount + 1);
    return {
      x: mousePosition.x - velocity.current.x * 100 * factor,
      y: mousePosition.y - velocity.current.y * 100 * factor,
      opacity: 0.3 * (1 - factor),
      scale: 1 - 0.2 * factor
    };
  });

  return (
    <>
      {/* Cursor trails */}
      {trails.map((trail, index) => (
        <motion.div
          key={index}
          className="cursor-trail"
          style={{
            left: trail.x,
            top: trail.y,
            opacity: trail.opacity * normalizedSpeed,
            scale: trail.scale
          }}
          transition={{ duration: 0.1 }}
        />
      ))}

      {/* Main cursor ring */}
      <motion.div
        ref={cursorRingRef}
        className={`cursor-ring ${cursorType} ${isClicking ? "clicking" : ""}`}
        style={{
          left: mousePosition.x,
          top: mousePosition.y,
          scale: isClicking ? 0.8 : 1 + normalizedSpeed * 0.2
        }}
        transition={{ duration: 0.15, ease: "circOut" }}
      >
        {cursorType === "button" && (
          <span className="cursor-text">Click</span>
        )}
        {cursorType === "input" && (
          <span className="cursor-text">Type</span>
        )}
        {cursorType === "media" && (
          <span className="cursor-text">View</span>
        )}
      </motion.div>

      {/* Cursor dot */}
      <motion.div
        className={`cursor-dot ${cursorType} ${isClicking ? "clicking" : ""}`}
        style={{
          left: mousePosition.x,
          top: mousePosition.y
        }}
        transition={{ duration: 0.05 }}
      />

      {/* Cursor styles */}
      <style jsx global>{`
        /* Hide default cursor */
        body, a, button, input, textarea, select, [role="button"], .clickable, .card, .hover-effect, img, video, canvas, .media {
          cursor: none !important;
        }

        /* Cursor ring */
        .cursor-ring {
          position: fixed;
          z-index: 9999;
          width: 30px;
          height: 30px;
          border: 2px solid #FF9B00;
          border-radius: 50%;
          pointer-events: none;
          transform: translate(-50%, -50%);
          transition: width 0.3s, height 0.3s, background-color 0.3s, border-color 0.3s;
          display: flex;
          align-items: center;
          justify-content: center;
          mix-blend-mode: exclusion;
        }

        /* Cursor dot */
        .cursor-dot {
          position: fixed;
          z-index: 10000;
          width: 6px;
          height: 6px;
          background-color: #FF9B00;
          border-radius: 50%;
          pointer-events: none;
          transform: translate(-50%, -50%);
          box-shadow: 0 0 10px rgba(255, 227, 0, 0.5);
        }

        /* Cursor trail */
        .cursor-trail {
          position: fixed;
          z-index: 9998;
          width: 8px;
          height: 8px;
          background-color: #FF9B00;
          border-radius: 50%;
          pointer-events: none;
          transform: translate(-50%, -50%);
        }

        /* Cursor text */
        .cursor-text {
          color: white;
          font-size: 10px;
          font-weight: bold;
          opacity: 0;
          transition: opacity 0.2s;
        }

        /* Button interaction */
        .cursor-ring.button {
          width: 50px;
          height: 50px;
          background-color: rgba(16, 5, 98, 0.2);
          border-color: #FF9B00;
        }

        .cursor-ring.button .cursor-text {
          opacity: 1;
        }

        /* Input interaction */
        .cursor-ring.input {
          width: 4px;
          height: 24px;
          border-radius: 2px;
          background-color: #FF9B00;
          border: none;
        }

        .cursor-dot.input {
          opacity: 0;
        }

        /* Media interaction */
        .cursor-ring.media {
          width: 60px;
          height: 60px;
          border-color: rgba(16, 5, 98, 0.8);
          background-color: rgba(255, 227, 0, 0.1);
        }

        .cursor-ring.media .cursor-text {
          opacity: 1;
        }

        /* Card interaction */
        .cursor-ring.card {
          width: 40px;
          height: 40px;
          background-color: rgba(16, 5, 98, 0.1);
        }

        /* Clicking state */
        .cursor-ring.clicking {
          background-color: rgba(255, 227, 0, 0.3);
        }

        .cursor-dot.clicking {
          background-color: rgba(16, 5, 98, 0.8);
        }

        /* Magnetic effect for buttons */
        .cursor-magnetic {
          transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        }
      `}</style>
    </>
  );
};

export default EnhancedCursor;