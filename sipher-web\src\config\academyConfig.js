export const academyConfig = {
  // Academy website URL - Update this with your actual academy website URL
  academyUrl: "https://sipherweb.com",

  // Card appearance delay (in milliseconds)
  showDelay: 2000,

  // Card text configuration
  text: {
    primary: "Visit Our",
    secondary: "Academic Website",
    tooltip: "Learn new skills at Sipher Academy"
  },

  // Features displayed in the card
  features: {
    courses: {
      icon: "BookOpen",
      label: "Courses",
      description: "Expert-led training"
    },
    certification: {
      icon: "Award",
      label: "Certified",
      description: "Industry recognized"
    },
    growth: {
      icon: "TrendingUp",
      label: "Growth",
      description: "Career advancement"
    }
  },

  // Statistics displayed
  stats: {
    rating: "4.9",
    students: "1000+",
    availability: "24/7"
  },

  // Analytics tracking
  analytics: {
    enabled: true,
    eventCategory: "Academy Button",
    eventLabel: "Academy Redirect"
  }
};
