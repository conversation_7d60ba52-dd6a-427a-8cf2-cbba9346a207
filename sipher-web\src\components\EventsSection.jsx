import React from 'react';
import { Calendar, MapPin, Clock, Users2, ArrowR<PERSON> } from 'lucide-react';

const EventsSection = () => {
  const events = [
    {
      title: "Web Development Workshop",
      date: "March 15, 2024",
      time: "10:00 AM - 2:00 PM",
      location: "Virtual Event",
      image: "https://images.unsplash.com/photo-1540575467063-178a50c2df87?w=800&h=400&fit=crop&q=80",
      attendees: 234,
      category: "Workshop"
    },
    {
      title: "Tech Innovation Summit",
      date: "March 20, 2024",
      time: "9:00 AM - 6:00 PM",
      location: "Lucknow, India",
      image: "https://images.unsplash.com/photo-1591115765373-5207764f72e7?w=800&h=400&fit=crop&q=80",
      attendees: 500,
      category: "Conference"
    },
    {
      title: "Design Systems Masterclass",
      date: "March 25, 2024",
      time: "2:00 PM - 5:00 PM",
      location: "Virtual Event",
      image: "https://images.unsplash.com/photo-1558403194-************?w=800&h=400&fit=crop&q=80",
      attendees: 156,
      category: "Masterclass"
    }
  ];

  return (
    <section className="py-20 bg-white">
      <div className="container mx-auto px-4">
        <div className="max-w-7xl mx-auto">
          {/* Section Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-blue-50 rounded-full mb-6">
              <Calendar className="w-5 h-5 text-[#100562]" />
              <span className="text-sm font-semibold text-[#100562]">Upcoming Events</span>
            </div>
            
            <h2 className="text-4xl font-bold text-gray-900 mb-6">
              Join Our Community
              <span className="relative ml-2">
                Events
                <div className="absolute left-0 -bottom-2 w-full h-2 bg-[#FF9B00] transform -skew-x-12 opacity-80"></div>
              </span>
            </h2>
            
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Participate in our upcoming events to learn, network, and grow with fellow community members.
            </p>
          </div>

          {/* Events Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {events.map((event, index) => (
              <div
                key={index}
                className="group bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300"
              >
                {/* Event Image */}
                <div className="relative h-48 overflow-hidden">
                  <img
                    src={event.image}
                    alt={event.title}
                    className="w-full h-full object-cover transform group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute top-4 left-4 px-3 py-1 bg-white/90 backdrop-blur-sm rounded-full text-sm font-medium text-[#100562]">
                    {event.category}
                  </div>
                </div>

                {/* Event Details */}
                <div className="p-6">
                  <h3 className="text-xl font-bold text-gray-900 mb-4 group-hover:text-[#100562] transition-colors duration-300">
                    {event.title}
                  </h3>

                  <div className="space-y-3 mb-6">
                    <div className="flex items-center gap-3 text-gray-600">
                      <Calendar className="w-5 h-5 text-[#100562]" />
                      <span>{event.date}</span>
                    </div>
                    <div className="flex items-center gap-3 text-gray-600">
                      <Clock className="w-5 h-5 text-[#100562]" />
                      <span>{event.time}</span>
                    </div>
                    <div className="flex items-center gap-3 text-gray-600">
                      <MapPin className="w-5 h-5 text-[#100562]" />
                      <span>{event.location}</span>
                    </div>
                    <div className="flex items-center gap-3 text-gray-600">
                      <Users2 className="w-5 h-5 text-[#100562]" />
                      <span>{event.attendees} Attendees</span>
                    </div>
                  </div>

                  <a
                    href="#register"
                    className="inline-flex items-center gap-2 text-[#100562] font-medium hover:gap-3 transition-all duration-300"
                  >
                    Register Now
                    <ArrowRight className="w-5 h-5" />
                  </a>
                </div>
              </div>
            ))}
          </div>

          {/* View All Events Button */}
          <div className="text-center mt-12">
            <a
              href="#all-events"
              className="inline-flex items-center gap-2 px-6 py-3 bg-gray-100 text-gray-900 rounded-xl hover:bg-gray-200 transition-all duration-300"
            >
              View All Events
              <ArrowRight className="w-5 h-5" />
            </a>
          </div>
        </div>
      </div>
    </section>
  );
}

export default EventsSection;