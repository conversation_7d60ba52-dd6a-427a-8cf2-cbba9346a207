import React from 'react';
import { Link } from "react-router-dom";
import { ArrowR<PERSON>, Shield, Sparkles } from 'lucide-react';

const CallToAction = () => {
  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <section className="relative py-12 md:py-10">
      {/* Subtle Background Pattern */}
      <div className="absolute inset-0 bg-[#F8FAFC] opacity-90"
       style={{ 
        backgroundImage: "url('./src/assets/bgCTA.jpg')",
        backgroundPosition: 'center',
        backgroundRepeat: 'norepeat',
        backgroundSize: 'cover',
       }} >
        <div
          className="absolute inset-0"
          style={{
            backgroundImage: "https://stockcake.com/i/vibrant-sound-waves_1253492_529738",
            backgroundSize: '24px 24px',
          }}
        ></div>
      </div>

      {/* Main Content */}
      <div className="relative container mx-auto px-4 md:px-6">
        <div className="max-w-4xl mx-auto">
          {/* Content Card */}
          <div className="bg-white rounded-lg shadow-md p-6 md:p-8">
            <div className="text-center">
              {/* Enterprise Badge */}
              <div className="inline-flex items-center gap-2 px-3 py-1 bg-blue-50 rounded-md mb-4">
                <Shield className="w-3 h-3 text-[#100562]" />
                <span className="text-xs font-semibold text-[#100562]">
                  Enterprise Solutions
                </span>
              </div>

              {/* Main Title */}
              <h2 className="text-2xl md:text-3xl font-bold text-[#1E293B] mb-4 leading-tight">
                Ready to Transform Your 
                <span className="relative whitespace-nowrap">
                  <span className="relative z-10 text-[#100562]"> Business</span>
                  <svg
                    aria-hidden="true"
                    viewBox="0 0 418 42"
                    className="absolute left-0 top-2/3 h-[0.5em] w-full fill-[#FF9B00] opacity-70"
                    preserveAspectRatio="none"
                  >
                    <path d="M203.371.916c-26.013-2.078-76.686 1.963-124.73 9.946L67.3 12.749C35.421 18.062 18.2 21.766 6.004 25.934 1.244 27.561.828 27.778.874 28.61c.07 1.214.828 1.121 9.595-1.176 9.072-2.377 17.15-3.92 39.246-7.496C123.565 7.986 157.869 4.492 195.942 5.046c7.461.108 19.25 1.696 19.17 2.582-.107 1.183-7.874 4.31-25.75 10.366-21.992 7.45-35.43 12.534-36.701 13.884-2.173 2.308-.202 4.407 4.442 4.734 2.654.187 3.263.157 15.593-.78 35.401-2.686 57.944-3.488 88.365-3.143 46.327.526 75.721 2.23 130.788 7.584 19.787 1.924 20.814 1.98 24.557 1.332l.066-.011c1.201-.203 1.53-1.825.399-2.335-2.911-1.31-4.893-1.604-22.048-3.261-57.509-5.556-87.871-7.36-132.059-7.842-23.239-.254-33.617-.116-50.627.674-11.629.54-42.371 2.494-46.696 2.967-2.359.259 8.133-3.625 26.504-9.81 23.239-7.825 27.934-10.149 28.304-14.005.417-4.348-3.529-6-16.878-7.066Z"></path>
                  </svg>
                </span>
                <br />
                Journey Today?
              </h2>

              {/* Description */}
              <p className="text-base text-gray-600 max-w-xl mx-auto mb-8 leading-relaxed">
                Join industry leaders who have already transformed their operations with our enterprise solutions. Let's build your success story together.
              </p>

              {/* Statistics */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
                <div className="p-3 bg-gray-50 rounded-md">
                  <div className="text-2xl font-bold text-[#100562] mb-1">98%</div>
                  <div className="text-xs text-gray-600">Client Satisfaction</div>
                </div>
                <div className="p-3 bg-gray-50 rounded-md">
                  <div className="text-2xl font-bold text-[#100562] mb-1">500+</div>
                  <div className="text-xs text-gray-600">Projects Delivered</div>
                </div>
                <div className="p-3 bg-gray-50 rounded-md">
                  <div className="text-2xl font-bold text-[#100562] mb-1">15+</div>
                  <div className="text-xs text-gray-600">Years Experience</div>
                </div>
              </div>

              {/* CTA Buttons */}
              <div className="flex flex-col sm:flex-row items-center justify-center gap-3">
                <Link
                  to="/contact"
                  className="group w-full sm:w-auto inline-flex items-center justify-center px-4 py-2 bg-[#100562] text-white rounded-md font-medium shadow-md hover:shadow-lg transition-all duration-300"
                  onClick={scrollToTop}
                >
                  <span className="flex items-center gap-2">
                    Schedule Consultation
                    <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-300" />
                  </span>
                </Link>

                <Link
                  to="/products"
                  className="w-full sm:w-auto inline-flex items-center justify-center px-4 py-2 bg-white text-[#100562] rounded-md font-medium border border-gray-200 hover:border-[#100562] transition-all duration-300"
                  onClick={scrollToTop}
                >
                  <span className="flex items-center gap-2">
                    <Sparkles className="w-4 h-4" />
                    View Solutions
                  </span>
                </Link>
              </div>
            </div>
          </div>

          
          </div>
        </div>
    </section>
  );
};

export default CallToAction;
